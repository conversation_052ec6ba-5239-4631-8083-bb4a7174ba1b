@echo off
echo === Quick Resume Push ===

REM Git path
set GIT_PATH="C:\Program Files\Git\bin\git.exe"

REM Check if Git exists
if not exist %GIT_PATH% (
    echo Error: Git not found
    pause
    exit /b 1
)

REM Quick push with default commit message
echo Adding files...
%GIT_PATH% add .

echo Committing changes...
%GIT_PATH% commit -m "Quick update - %date% %time%"

echo Pushing to GitHub...
%GIT_PATH% push origin main

if %errorlevel% equ 0 (
    echo.
    echo Success! Pushed to GitHub
    echo Repository: https://github.com/ChuYuki/resume
) else (
    echo.
    echo Push failed!
)

echo.
pause
